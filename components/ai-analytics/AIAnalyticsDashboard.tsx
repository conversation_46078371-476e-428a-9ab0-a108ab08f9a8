'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import AIInsightsSummary from './AIInsightsSummary';
import AIInsightsContent from './AIInsightsContent';
import SubscriptionLimitDisplay from './SubscriptionLimitDisplay';
import { 
  fetchAIAnalytics, 
  getAIAnalyticsUsage, 
  checkAIAnalyticsAccess,
  AnalyticsPeriod 
} from '@/lib/ai-analytics';
import { AIAnalyticsResponse, AIAnalyticsUsage } from '@/types/subscription';

const AIAnalyticsDashboard: React.FC = () => {
  const { companies } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<AnalyticsPeriod>('monthly');
  const [analyticsData, setAnalyticsData] = useState<AIAnalyticsResponse | null>(null);
  const [usage, setUsage] = useState<AIAnalyticsUsage | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'summary' | 'detailed'>('summary');

  // Check if user has access to AI Analytics
  const access = checkAIAnalyticsAccess();

  const fetchData = useCallback(async () => {
    if (!companies || companies.length === 0) {
      setError('No company selected');
      return;
    }

    const companyId = companies[0].company_id;
    
    try {
      setIsLoading(true);
      setError(null);

      // Fetch usage data
      const usageData = await getAIAnalyticsUsage();
      setUsage(usageData);

      // Fetch analytics data
      const data = await fetchAIAnalytics(companyId, selectedPeriod);
      setAnalyticsData(data);
    } catch (err: any) {
      console.error('Error fetching AI analytics:', err);
      setError(err.message || 'Failed to fetch AI analytics data');
    } finally {
      setIsLoading(false);
    }
  }, [companies, selectedPeriod]);

  useEffect(() => {
    if (access.enabled) {
      fetchData();
    }
  }, [fetchData, access.enabled]);

  const handlePeriodChange = (period: AnalyticsPeriod) => {
    setSelectedPeriod(period);
  };

  const handleRefresh = () => {
    fetchData();
  };

  if (!access.enabled) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-secondary-dark">AI Analytics</h1>
        </div>
        
        <DashboardCard title="AI Analytics Not Available">
          <div className="text-center py-8">
            <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">AI Analytics Not Available</h3>
            <p className="text-gray-600 mb-4">
              AI Analytics is not included in your current subscription plan.
            </p>
            <a
              href="/dashboard/hr/subscription"
              className="btn-primary"
            >
              Upgrade Plan
            </a>
          </div>
        </DashboardCard>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">AI Analytics</h1>
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="btn-outline flex items-center"
        >
          <svg 
            className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {/* Usage Display */}
      {usage && (
        <SubscriptionLimitDisplay usage={usage} isLoading={isLoading} />
      )}

      {/* Period Selection */}
      <DashboardCard title="Analysis Period">
        <div className="flex flex-wrap gap-2">
          {(['daily', 'weekly', 'monthly', 'annual'] as AnalyticsPeriod[]).map((period) => (
            <button
              key={period}
              onClick={() => handlePeriodChange(period)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedPeriod === period
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </button>
          ))}
        </div>
      </DashboardCard>

      {/* Loading State */}
      {isLoading && (
        <DashboardCard title="Loading AI Analytics">
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Generating AI insights...</p>
          </div>
        </DashboardCard>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <DashboardCard title="Error">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <p className="font-medium">Unable to load AI analytics</p>
            <p className="text-sm mt-1">{error}</p>
            <button
              onClick={handleRefresh}
              className="mt-2 text-sm underline hover:no-underline"
            >
              Try again
            </button>
          </div>
        </DashboardCard>
      )}

      {/* Analytics Data */}
      {analyticsData && !isLoading && !error && (
        <>
          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('summary')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'summary'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Summary & Insights
              </button>
              <button
                onClick={() => setActiveTab('detailed')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'detailed'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Detailed Analysis
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'summary' && (
            <DashboardCard title="AI Insights Summary">
              <AIInsightsSummary insights={analyticsData.ai_insights} />
            </DashboardCard>
          )}

          {activeTab === 'detailed' && (
            <DashboardCard title="">
              <AIInsightsContent insights={analyticsData.ai_insights} />
            </DashboardCard>
          )}
        </>
      )}
    </div>
  );
};

export default AIAnalyticsDashboard;
