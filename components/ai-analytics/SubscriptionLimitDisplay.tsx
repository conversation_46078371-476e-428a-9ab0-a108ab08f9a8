'use client';

import React from 'react';
import { AIAnalyticsUsage } from '@/types/subscription';

interface SubscriptionLimitDisplayProps {
  usage: AIAnalyticsUsage;
  isLoading?: boolean;
}

const SubscriptionLimitDisplay: React.FC<SubscriptionLimitDisplayProps> = ({ 
  usage, 
  isLoading = false 
}) => {
  if (isLoading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-blue-200 rounded w-1/3 mb-2"></div>
          <div className="h-3 bg-blue-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const isUnlimited = usage.monthly_limit === null;
  const usagePercentage = isUnlimited ? 0 : (usage.current_month_usage / usage.monthly_limit) * 100;
  const isNearLimit = usagePercentage >= 80;
  const isAtLimit = usagePercentage >= 100;

  const getStatusColor = () => {
    if (isAtLimit) return 'red';
    if (isNearLimit) return 'yellow';
    return 'blue';
  };

  const statusColor = getStatusColor();
  const resetDate = new Date(usage.reset_date).toLocaleDateString();

  return (
    <div className={`bg-${statusColor}-50 border border-${statusColor}-200 rounded-lg p-4`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className={`text-sm font-medium text-${statusColor}-800`}>
          AI Analytics Usage
        </h4>
        {isAtLimit && (
          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
            Limit Reached
          </span>
        )}
      </div>

      <div className="space-y-2">
        {isUnlimited ? (
          <p className={`text-sm text-${statusColor}-700`}>
            Unlimited AI Analytics requests available
          </p>
        ) : (
          <>
            <div className="flex justify-between items-center">
              <span className={`text-sm text-${statusColor}-700`}>
                {usage.current_month_usage} of {usage.monthly_limit} requests used
              </span>
              <span className={`text-xs text-${statusColor}-600`}>
                {Math.round(usagePercentage)}%
              </span>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`bg-${statusColor}-500 h-2 rounded-full transition-all duration-300`}
                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
              ></div>
            </div>

            <div className="flex justify-between items-center text-xs">
              <span className={`text-${statusColor}-600`}>
                {usage.remaining_requests !== null && usage.remaining_requests >= 0
                  ? `${usage.remaining_requests} requests remaining`
                  : 'No requests remaining'
                }
              </span>
              <span className={`text-${statusColor}-600`}>
                Resets: {resetDate}
              </span>
            </div>
          </>
        )}
      </div>

      {isAtLimit && (
        <div className="mt-3 p-3 bg-red-100 rounded-md">
          <p className="text-sm text-red-800">
            You've reached your monthly AI Analytics limit. 
            <a href="/dashboard/hr/subscription" className="font-medium underline ml-1">
              Upgrade your plan
            </a> for more requests or wait until {resetDate} for the limit to reset.
          </p>
        </div>
      )}

      {isNearLimit && !isAtLimit && (
        <div className="mt-3 p-3 bg-yellow-100 rounded-md">
          <p className="text-sm text-yellow-800">
            You're approaching your monthly limit. Consider 
            <a href="/dashboard/hr/subscription" className="font-medium underline ml-1">
              upgrading your plan
            </a> for unlimited AI Analytics.
          </p>
        </div>
      )}
    </div>
  );
};

export default SubscriptionLimitDisplay;
