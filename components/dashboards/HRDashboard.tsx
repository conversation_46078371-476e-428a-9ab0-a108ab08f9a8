'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import DashboardStats from '@/components/ui/DashboardStats';
import DashboardChart from '@/components/ui/DashboardChart';
import CompanyRegistrationModal from '@/components/company/CompanyRegistrationModal';
import CompanyDisplay from '@/components/company/CompanyDisplay';
import EmployeeRegistrationModal from '@/components/employee/EmployeeRegistrationModal';
import { checkAIAnalyticsAccess } from '@/lib/ai-analytics';

// Define employee interface
interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  status: string;
  hire_date: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  id_number: string | null;
}

const HRDashboard = () => {
  const { companies, user, refreshUserData } = useAuth();
  const [isCompanyModalOpen, setIsCompanyModalOpen] = useState(false);
  const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false);
  const hasCompany = companies && companies.length > 0;

  // State for employee, department, and attendance count
  const [employeeStats, setEmployeeStats] = useState({
    totalEmployees: '0',
    presentToday: '0',
    onLeave: '0',
    absentToday: '0',
    totalDepartments: '0',
    attendancePercentage: '0',
    isLoading: true
  });



  // State for attendance chart data
  const [attendanceChartData, setAttendanceChartData] = useState({
    labels: ['Present', 'Absent', 'On Leave'],
    values: [0, 0, 0],
    isLoading: true
  });



  // State for recent hires
  const [recentHires, setRecentHires] = useState<Employee[]>([]);
  const [isLoadingRecentHires, setIsLoadingRecentHires] = useState(true);

  // Refresh user data when the dashboard loads (only once)
  useEffect(() => {
    if (user) {
      refreshUserData();
    }
  }, [user]); // Only depend on user, not refreshUserData

  // Function to fetch employee and department data
  const fetchData = async () => {
    try {
      if (!companies || companies.length === 0) {
        return;
      }

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Define response interfaces
      interface EmployeeResponse {
        extend: {
          employees: Employee[];
          pagination: {
            has_next: boolean;
            has_prev: boolean;
            page: number;
            pages: number;
            per_page: number;
            total_count: number;
          };
        };
        msg: string;
      }

      interface DepartmentResponse {
        departments: {
          department_id: string;
          name: string;
          description: string;
          manager_id: string | null;
          created_at: string;
          updated_at: string;
        }[];
        pagination: {
          has_next: boolean;
          has_prev: boolean;
          page: number;
          pages: number;
          per_page: number;
          total_count: number;
        };
        success: boolean;
      }

      // Fetch employees
      const employeeResponse = await apiGet<EmployeeResponse>(`api/employees?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Fetch departments
      const departmentResponse = await apiGet<DepartmentResponse>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Get employee count
      const totalEmployees = employeeResponse.extend && employeeResponse.extend.pagination
        ? employeeResponse.extend.pagination.total_count.toString()
        : '0';

      // Get department count
      const totalDepartments = departmentResponse.pagination
        ? departmentResponse.pagination.total_count.toString()
        : '0';

      // Fetch daily attendance data
      try {
        const attendanceResponse = await apiGet<{
          summary: {
            present_count: number;
            absent_count: number;
            on_leave_count: number;
            attendance_percentage: number;
          }
        }>(`api/attendance/daily?company_id=${companyId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        // Update stats with real data
        setEmployeeStats({
          totalEmployees,
          presentToday: attendanceResponse.summary ? attendanceResponse.summary.present_count.toString() : '0',
          onLeave: attendanceResponse.summary ? attendanceResponse.summary.on_leave_count.toString() : '0',
          absentToday: attendanceResponse.summary ? attendanceResponse.summary.absent_count.toString() : '0',
          totalDepartments,
          attendancePercentage: attendanceResponse.summary ? attendanceResponse.summary.attendance_percentage.toFixed(1) : '0',
          isLoading: false
        });

        // Update chart data with today's attendance
        if (attendanceResponse.summary) {
          setAttendanceChartData({
            labels: ['Present', 'Absent', 'On Leave'],
            values: [
              attendanceResponse.summary.present_count,
              attendanceResponse.summary.absent_count,
              attendanceResponse.summary.on_leave_count
            ],
            isLoading: false
          });
        } else {
          // Set default chart data if no summary is available
          setAttendanceChartData({
            labels: ['Present', 'Absent', 'On Leave'],
            values: [0, 0, 0],
            isLoading: false
          });
        }
      } catch (error) {
        console.error('Error fetching attendance data:', error);
        // Fallback to estimated values if attendance API fails
        setEmployeeStats({
          totalEmployees,
          presentToday: Math.max(0, Math.floor(parseInt(totalEmployees) * 0.8)).toString(),
          onLeave: Math.max(0, Math.floor(parseInt(totalEmployees) * 0.1)).toString(),
          absentToday: Math.max(0, Math.floor(parseInt(totalEmployees) * 0.05)).toString(),
          totalDepartments,
          attendancePercentage: '80.0',
          isLoading: false
        });

        // Set default chart data
        setAttendanceChartData({
          labels: ['Present', 'Absent', 'On Leave'],
          values: [0, 0, 0],
          isLoading: false
        });
      }



      // Process recent hires
      if (employeeResponse.extend && employeeResponse.extend.employees) {
        // Sort employees by hire_date or created_at (most recent first)
        const sortedEmployees = [...employeeResponse.extend.employees].sort((a, b) => {
          // First try to sort by hire_date if available
          if (a.hire_date && b.hire_date) {
            return new Date(b.hire_date).getTime() - new Date(a.hire_date).getTime();
          }
          // Fall back to created_at
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        // Take the 5 most recent hires
        setRecentHires(sortedEmployees.slice(0, 5));
      }

      setIsLoadingRecentHires(false);
    } catch (error) {
      console.error('Error fetching data:', error);
      // Set mock data if API calls fail
      setEmployeeStats({
        totalEmployees: '124',
        presentToday: '112',
        onLeave: '8',
        absentToday: '4',
        totalDepartments: '5',
        attendancePercentage: '90.3',
        isLoading: false
      });
      setIsLoadingRecentHires(false);
    }
  };

  // Fetch data when component mounts
  useEffect(() => {
    fetchData();
  }, []);

  // Dashboard stats using real data or fallback to mock data
  const stats = [
    { title: 'Total Employees', value: employeeStats.isLoading ? '...' : employeeStats.totalEmployees, change: '+5%', changeType: 'positive' },
    { title: 'Present Today', value: employeeStats.isLoading ? '...' : employeeStats.presentToday, change: '+3%', changeType: 'positive' },
    { title: 'Departments', value: employeeStats.isLoading ? '...' : employeeStats.totalDepartments, change: '+2%', changeType: 'positive' },
    { title: 'On Leave', value: employeeStats.isLoading ? '...' : employeeStats.onLeave, change: '-2%', changeType: 'positive' },
    { title: 'Absent Today', value: employeeStats.isLoading ? '...' : employeeStats.absentToday, change: '+1%', changeType: 'negative' },
  ];

  const openCompanyModal = () => setIsCompanyModalOpen(true);
  const closeCompanyModal = () => setIsCompanyModalOpen(false);

  const openEmployeeModal = () => setIsEmployeeModalOpen(true);
  const closeEmployeeModal = () => setIsEmployeeModalOpen(false);

  const pendingRequests = [
    { id: 1, type: 'Leave Request', employee: 'Michael Brown', date: 'May 15-20', status: 'Pending' },
    { id: 2, type: 'Attendance Correction', employee: 'Emily Davis', date: 'May 10', status: 'Pending' },
    { id: 3, type: 'Shift Change', employee: 'Robert Wilson', date: 'May 25', status: 'Pending' },
    { id: 4, type: 'Leave Request', employee: 'Jane Smith', date: 'May 22-24', status: 'Pending' },
    { id: 5, type: 'Attendance Correction', employee: 'John Doe', date: 'May 12', status: 'Pending' },
  ];

  return (
    <div className="space-y-6">
      {/* Company Registration Modal */}
      <CompanyRegistrationModal
        isOpen={isCompanyModalOpen}
        onClose={closeCompanyModal}
        onSuccess={closeCompanyModal}
      />

      {/* Employee Registration Modal */}
      <EmployeeRegistrationModal
        isOpen={isEmployeeModalOpen}
        onClose={closeEmployeeModal}
        onSuccess={() => {
          // Refresh data after adding an employee
          fetchData();
          closeEmployeeModal();
        }}
      />

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">HR Dashboard</h1>
        <div>
          {hasCompany && (
            <button
              className="btn-primary py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center"
              onClick={openEmployeeModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Employee
            </button>
          )}
          {!hasCompany && (
            <button
              className="btn-secondary py-2 px-4 text-sm font-medium rounded-md shadow-sm transition-all hover:shadow-md flex items-center"
              onClick={openCompanyModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              Register Company
            </button>
          )}
        </div>
      </div>

      {/* Company Display */}
      {hasCompany ? (
        <CompanyDisplay />
      ) : (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md text-sm mb-6">
          <p>
            <strong>Welcome to KaziSync!</strong> To get started, please register your company by clicking the "Register Company" button above.
            This will allow you to manage your employees, attendance, and more.
          </p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          // Define the link based on the stat title
          let link = '';
          if (stat.title === 'Total Employees') {
            link = '/dashboard/hr/employees';
          } else if (stat.title === 'Present Today' || stat.title === 'On Leave' || stat.title === 'Absent Today') {
            link = '/dashboard/hr/attendance/daily';
          } else if (stat.title === 'Departments') {
            link = '/dashboard/hr/departments';
          }

          return (
            <Link href={link} key={index}>
              <div className="cursor-pointer transition-transform hover:scale-105">
                <DashboardStats
                  title={stat.title}
                  value={stat.value}
                  change={stat.change}
                  changeType={stat.changeType as 'positive' | 'negative' | 'neutral'}
                />
              </div>
            </Link>
          );
        })}
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Attendance Chart */}
        <div className="lg:col-span-2">
          <DashboardCard title="Attendance Overview">
            {attendanceChartData.isLoading ? (
              <div className="py-8 text-center h-[250px] flex flex-col items-center justify-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="mt-2 text-secondary">Loading attendance data...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <DashboardChart
                    type="doughnut"
                    labels={attendanceChartData.labels}
                    datasets={[
                      {
                        data: attendanceChartData.values,
                        backgroundColor: ['#4F46E5', '#EF4444', '#F59E0B'],
                        borderColor: ['#4338CA', '#DC2626', '#D97706'],
                        borderWidth: 1,
                      }
                    ]}
                    height="250px"
                  />
                </div>
                <div className="flex flex-col justify-center">
                  <h3 className="text-lg font-medium text-secondary-dark mb-4">Today's Attendance</h3>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-[#4F46E5] mr-2"></div>
                      <span className="text-sm text-secondary-dark">Present: {attendanceChartData.values[0]}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-[#EF4444] mr-2"></div>
                      <span className="text-sm text-secondary-dark">Absent: {attendanceChartData.values[1]}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-[#F59E0B] mr-2"></div>
                      <span className="text-sm text-secondary-dark">On Leave: {attendanceChartData.values[2]}</span>
                    </div>
                    <div className="mt-4">
                      <Link href="/dashboard/hr/attendance/daily" className="text-sm text-primary hover:text-primary-dark">
                        View detailed report →
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </DashboardCard>
        </div>

        {/* Pending Requests */}
        <div>
          <DashboardCard title="Pending Requests">
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {pendingRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between border-b border-gray-100 pb-2 last:border-0">
                  <div>
                    <p className="text-sm font-medium text-secondary-dark">{request.type}</p>
                    <p className="text-xs text-secondary">{request.employee} • {request.date}</p>
                  </div>
                  <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                    {request.status}
                  </span>
                </div>
              ))}
            </div>
          </DashboardCard>
        </div>
      </div>

      {/* Recent Hires and Upcoming Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Hires */}
        <DashboardCard title="Recent Hires">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {isLoadingRecentHires ? (
              <div className="py-8 text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <p className="mt-2 text-secondary">Loading recent hires...</p>
              </div>
            ) : recentHires.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">No recent hires found.</p>
              </div>
            ) : (
              recentHires.map((employee) => {
                // Calculate how long ago the employee was hired
                const hireDate = employee.hire_date ? new Date(employee.hire_date) : new Date(employee.created_at);
                const today = new Date();
                const diffTime = Math.abs(today.getTime() - hireDate.getTime());
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                let joinedText = '';
                if (diffDays === 0) {
                  joinedText = 'Joined today';
                } else if (diffDays === 1) {
                  joinedText = 'Joined yesterday';
                } else {
                  joinedText = `Joined ${diffDays} days ago`;
                }

                // Get initials for the avatar
                const initials = employee.first_name.charAt(0) + employee.last_name.charAt(0);

                return (
                  <div key={employee.employee_id} className="flex items-center border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                    <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center mr-3">
                      <span className="text-sm font-medium">
                        {initials}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-secondary-dark">
                        {employee.full_name}
                      </p>
                      <p className="text-xs text-secondary">
                        {employee.position || 'No position specified'}
                      </p>
                      <p className="text-xs text-secondary-light">
                        {joinedText}
                      </p>
                    </div>
                  </div>
                );
              })
            )}
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/hr/employees" className="text-sm text-primary hover:text-primary-dark">
              View all employees
            </Link>
          </div>
        </DashboardCard>

        {/* Recent Announcements */}
        <DashboardCard title="Recent Announcements">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            <div className="py-8 text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
              </svg>
              <p className="mt-2 text-secondary">No announcements yet</p>
              <Link href="/dashboard/hr/announcements" className="mt-2 text-primary hover:text-primary-dark text-sm">
                Create your first announcement
              </Link>
            </div>
          </div>
          <div className="mt-4 text-center">
            <Link href="/dashboard/hr/announcements" className="text-sm text-primary hover:text-primary-dark">
              Manage announcements →
            </Link>
          </div>
        </DashboardCard>
      </div>

      {/* Additional Section for Upcoming Events */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Birthdays & Anniversaries */}
        <DashboardCard title="Upcoming Birthdays & Anniversaries">
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {[1, 2, 3, 4].map((_, index) => (
              <div key={index} className="flex items-center border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                <div className={`h-10 w-10 rounded-full ${
                  index % 2 === 0 ? 'bg-purple-500' : 'bg-pink-500'
                } text-white flex items-center justify-center mr-3`}>
                  <span className="text-sm">
                    {index % 2 === 0 ? '🎂' : '🎉'}
                  </span>
                </div>
                <div>
                  <p className="text-sm font-medium text-secondary-dark">
                    {['Jane Smith', 'Emily Davis', 'Robert Wilson', 'Michael Brown'][index % 4]}
                  </p>
                  <p className="text-xs text-secondary">
                    {index % 2 === 0 ? 'Birthday' : 'Work Anniversary'} • {['May 15', 'May 20', 'May 25', 'May 30'][index % 4]}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </DashboardCard>

        {/* Quick Actions */}
        <DashboardCard title="Quick Actions">
          <div className="grid grid-cols-2 gap-4">
            <Link href="/dashboard/hr/announcements" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
              <svg className="mx-auto h-8 w-8 text-blue-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Announcements</p>
              <p className="text-xs text-gray-500">Manage company communications</p>
            </Link>

            <Link href="/dashboard/hr/employees" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
              <svg className="mx-auto h-8 w-8 text-green-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Employees</p>
              <p className="text-xs text-gray-500">Manage employee records</p>
            </Link>

            <Link href="/dashboard/hr/leave" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
              <svg className="mx-auto h-8 w-8 text-yellow-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Leave Management</p>
              <p className="text-xs text-gray-500">Handle leave requests</p>
            </Link>

            <Link href="/dashboard/hr/reports" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center">
              <svg className="mx-auto h-8 w-8 text-purple-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="text-sm font-medium text-gray-900">Reports</p>
              <p className="text-xs text-gray-500">View analytics & reports</p>
            </Link>

            {/* AI Analytics - Show only if feature is enabled */}
            {checkAIAnalyticsAccess().enabled && (
              <Link href="/dashboard/hr/ai-analytics" className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-center relative">
                <div className="absolute top-2 right-2">
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800">
                    AI
                  </span>
                </div>
                <svg className="mx-auto h-8 w-8 text-indigo-600 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <p className="text-sm font-medium text-gray-900">AI Analytics</p>
                <p className="text-xs text-gray-500">AI-powered insights</p>
              </Link>
            )}
          </div>
        </DashboardCard>
      </div>
    </div>
  );
};

export default HRDashboard;
