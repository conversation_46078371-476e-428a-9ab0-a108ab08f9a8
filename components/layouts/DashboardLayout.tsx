"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getUnreadAnnouncementsCount } from '@/lib/announcements';
import { checkAIAnalyticsAccess } from '@/lib/ai-analytics';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {
    // On mobile devices, start with sidebar closed
    return typeof window !== 'undefined' ? window.innerWidth > 768 : true;
  });
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [unreadAnnouncementsCount, setUnreadAnnouncementsCount] = useState(0);

  // Add window resize listener to handle responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      }
    };

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Call handler right away so state gets updated with initial window size
    handleResize();

    // Remove event listener on cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch unread announcements count for employees
  useEffect(() => {
    const fetchUnreadCount = async () => {
      if (user?.role === 'employee' || user?.role === 'manager') {
        try {
          const count = await getUnreadAnnouncementsCount();
          setUnreadAnnouncementsCount(count);
        } catch (error) {
          console.error('Error fetching unread announcements count:', error);
        }
      }
    };

    fetchUnreadCount();

    // Refresh count every 5 minutes
    const interval = setInterval(fetchUnreadCount, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user?.role]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
    if (isNotificationsOpen) setIsNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen);
    if (isProfileDropdownOpen) setIsProfileDropdownOpen(false);
  };

  // Determine user role from auth context or fallback to URL for demo purposes
  const userRole = user?.role
    ? user.role === 'super-admin'
      ? 'Super Admin'
      : user.role === 'admin'
      ? 'Company Admin'
      : user.role === 'hr'
      ? 'HR Manager'
      : user.role === 'manager'
      ? 'Manager'
      : 'Employee'
    : pathname.includes('super-admin')
    ? 'Super Admin'
    : pathname.includes('admin')
    ? 'Company Admin'
    : pathname.includes('hr')
    ? 'HR Manager'
    : pathname.includes('manager')
    ? 'Manager'
    : 'Employee';

  // Navigation items based on user role
  const navigationItems = getNavigationItems(userRole, unreadAnnouncementsCount);

  return (
    <div className="min-h-screen bg-background flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm z-10 relative">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="text-secondary-dark hover:text-primary p-2 rounded-md"
              aria-label="Toggle sidebar"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            <Link href="/" className="flex items-center space-x-2 ml-2">
              <span className="text-xl font-bold text-primary">KaziSync</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            {/* Search
            <div className="hidden md:block relative">
              <input
                type="text"
                placeholder="Search..."
                className="w-64 px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-secondary absolute left-3 top-2.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div> */}

            {/* Notifications */}
            {/* <div className="relative">
              <button
                onClick={toggleNotifications}
                className="text-secondary-dark hover:text-primary p-2 rounded-md relative"
                aria-label="Notifications"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                  />
                </svg>
                <span className="absolute top-1 right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  3
                </span>
              </button> */}

              {/* Notifications Dropdown */}
              {/* {isNotificationsOpen && (
                <div className="absolute right-0 mt-2 w-80 bg-white rounded-xl border border-gray-200 py-1 z-10 animate-fade-in">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <h3 className="text-sm font-semibold text-secondary-dark">Notifications</h3>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    <div className="px-4 py-3 border-b border-gray-100 hover:bg-gray-50">
                      <p className="text-sm font-medium text-secondary-dark">New leave request</p>
                      <p className="text-xs text-secondary mt-1">John Doe requested annual leave</p>
                      <p className="text-xs text-secondary-light mt-1">2 minutes ago</p>
                    </div>
                    <div className="px-4 py-3 border-b border-gray-100 hover:bg-gray-50">
                      <p className="text-sm font-medium text-secondary-dark">Missed clock-out</p>
                      <p className="text-xs text-secondary mt-1">You missed your clock-out yesterday</p>
                      <p className="text-xs text-secondary-light mt-1">5 hours ago</p>
                    </div>
                    <div className="px-4 py-3 hover:bg-gray-50">
                      <p className="text-sm font-medium text-secondary-dark">Shift change</p>
                      <p className="text-xs text-secondary mt-1">Your shift for next week has been updated</p>
                      <p className="text-xs text-secondary-light mt-1">1 day ago</p>
                    </div>
                  </div>
                  <div className="px-4 py-2 border-t border-gray-100 text-center">
                    <Link href="/notifications" className="text-xs text-primary hover:text-primary-dark">
                      View all notifications
                    </Link>
                  </div>
                </div>
              )}
            </div> */}

            {/* Profile */}
            <div className="relative">
              <button
                onClick={toggleProfileDropdown}
                className="flex items-center space-x-2 text-secondary-dark hover:text-primary"
                aria-label="User menu"
              >
                <div className="h-8 w-8 rounded-full bg-primary text-white flex items-center justify-center">
                  <span className="text-sm font-medium">{user?.name ? user.name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2) : 'JD'}</span>
                </div>
                <span className="hidden md:block text-sm font-medium">{user?.name || 'John Doe'}</span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 hidden md:block"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {/* Profile Dropdown */}
              {isProfileDropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md border border-gray-200 py-1 z-10 animate-fade-in">
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-secondary-dark">{user?.name || 'John Doe'}</p>
                    <p className="text-xs text-secondary">{userRole}</p>
                  </div>
                  <Link
                    href="/profile"
                    className="block px-4 py-2 text-sm text-secondary-dark hover:bg-gray-50"
                  >
                    Your Profile
                  </Link>
                  <Link
                    href="/settings"
                    className="block px-4 py-2 text-sm text-secondary-dark hover:bg-gray-50"
                  >
                    Settings
                  </Link>
                  <div className="border-t border-gray-100 mt-1 pt-1">
                    <button
                      onClick={logout}
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-50"
                    >
                      Sign out
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Mobile Overlay when open */}
        {isSidebarOpen && (
          <div
            className="md:hidden fixed inset-0 bg-gray-600 bg-opacity-75 z-20"
            onClick={toggleSidebar}
            aria-hidden="true"
          ></div>
        )}

        {/* Sidebar */}
        <aside
          className={`bg-white border-r border-gray-200 z-30 transition-all duration-300 ${
            isSidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
          } ${
            isSidebarOpen ? 'md:w-64' : 'md:w-16'
          } fixed md:static inset-y-0 left-0 flex flex-col md:flex`}
        >
          <nav className="flex-1 py-4 overflow-y-auto">
            <ul className="space-y-1 px-2">
              {navigationItems.map((item, index) => (
                <li key={index}>
                  <Link
                    href={item.href}
                    className={`flex items-center justify-between px-3 py-2 rounded-md ${
                      pathname === item.href
                        ? 'bg-primary-light bg-opacity-10 text-primary'
                        : 'text-secondary-dark hover:bg-gray-100'
                    }`}
                    onClick={() => {
                      // Close sidebar on mobile after navigation
                      if (window.innerWidth < 768) {
                        setIsSidebarOpen(false);
                      }
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-xl">{item.icon}</span>
                      {(isSidebarOpen || window.innerWidth < 768) && <span className="text-sm font-medium">{item.name}</span>}
                    </div>
                    {(item as any).badge && (isSidebarOpen || window.innerWidth < 768) && (
                      <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center">
                        {(item as any).badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 md:ml-0">
          {children}
        </main>
      </div>
    </div>
  );
};

// Helper function to get navigation items based on user role
function getNavigationItems(role: string, unreadCount: number = 0) {
  const commonItems = [
    {
      name: 'Dashboard',
      href: getRoleBasedPath(role, ''),
      icon: '📊',
    },
    {
      name: 'Profile',
      href: '/profile',
      icon: '👤',
    },
  ];

  // Normalize role for consistent lookup
  const normalizedRole = role.toLowerCase().replace(/\s+/g, '-');

  const roleSpecificItems = {
    'super-admin': [
      {
        name: 'Countries',
        href: '/dashboard/super-admin/countries',
        icon: '🌍',
      },
      {
        name: 'Companies',
        href: '/dashboard/super-admin/companies',
        icon: '🏢',
      },
      {
        name: 'Subscriptions',
        href: '/dashboard/super-admin/subscriptions',
        icon: '💰',
      },
      {
        name: 'System Settings',
        href: '/dashboard/super-admin/settings',
        icon: '⚙️',
      },
      {
        name: 'Audit Logs',
        href: '/dashboard/super-admin/audit-logs',
        icon: '📝',
      },
    ],
    'company-admin': [
      {
        name: 'Employees',
        href: '/dashboard/admin/employees',
        icon: '👥',
      },
      {
        name: 'Departments',
        href: '/dashboard/admin/departments',
        icon: '🏗️',
      },
      {
        name: 'Locations',
        href: '/dashboard/admin/locations',
        icon: '📍',
      },
      {
        name: 'Reports',
        href: '/dashboard/admin/reports',
        icon: '📈',
      },
      {
        name: 'Settings',
        href: '/dashboard/admin/settings',
        icon: '⚙️',
      },
    ],
    'hr-manager': [
      {
        name: 'Employees',
        href: '/dashboard/hr/employees',
        icon: '👥',
      },
      {
        name: 'Departments',
        href: '/dashboard/hr/departments',
        icon: '🏢',
      },
      {
        name: 'Announcements',
        href: '/dashboard/hr/announcements',
        icon: '📢',
      },
      {
        name: 'Attendance',
        href: '/dashboard/hr/attendance',
        icon: '🕒',
      },
      {
        name: 'Payroll',
        href: '/dashboard/hr/payroll',
        icon: '💰',
      },
      {
        name: 'Leave Management',
        href: '/dashboard/hr/leave',
        icon: '🗓️',
      },
      {
        name: 'Events',
        href: '/dashboard/hr/events',
        icon: '🎉',
      },
      {
        name: 'Shifts',
        href: '/dashboard/hr/shifts',
        icon: '⏱️',
      },
      {
        name: 'Reports',
        href: '/dashboard/hr/reports',
        icon: '📊',
      },
      // Add AI Analytics only if feature is enabled
      ...(checkAIAnalyticsAccess().enabled ? [{
        name: 'AI Analytics',
        href: '/dashboard/hr/ai-analytics',
        icon: '🤖',
      }] : []),
      {
        name: 'Subscription',
        href: '/dashboard/hr/subscription',
        icon: '💳',
      },
      {
        name: 'Settings',
        href: '/dashboard/hr/settings',
        icon: '⚙️',
      },
    ],
    'manager': [
      {
        name: 'Team',
        href: '/dashboard/manager/team',
        icon: '👥',
      },
      {
        name: 'Attendance',
        href: '/dashboard/manager/attendance',
        icon: '🕒',
      },
      {
        name: 'Leave Requests',
        href: '/dashboard/manager/leave-requests',
        icon: '🗓️',
      },
      {
        name: 'Shifts',
        href: '/dashboard/manager/shifts',
        icon: '⏱️',
      },
    ],
    'employee': [
      {
        name: 'Announcements',
        href: '/dashboard/employee/announcements',
        icon: '📢',
        badge: unreadCount > 0 ? unreadCount : undefined,
      },
      {
        name: 'Attendance',
        href: '/dashboard/employee/attendance',
        icon: '🕒',
      },
      {
        name: 'Leave',
        href: '/dashboard/employee/leave',
        icon: '🗓️',
      },
      {
        name: 'Shifts',
        href: '/dashboard/employee/shifts',
        icon: '⏱️',
      },
    ],
  };

  return [...commonItems, ...(roleSpecificItems[normalizedRole as keyof typeof roleSpecificItems] || [])];
}

// Helper function to get the base path for each role
function getRoleBasedPath(role: string, subpath: string) {
  // Normalize role for consistent lookup
  const normalizedRole = role.toLowerCase().replace(/\s+/g, '-');

  const basePath = normalizedRole === 'super-admin'
    ? '/dashboard/super-admin'
    : normalizedRole === 'company-admin' || normalizedRole === 'admin'
    ? '/dashboard/admin'
    : normalizedRole === 'hr-manager' || normalizedRole === 'hr'
    ? '/dashboard/hr'
    : normalizedRole === 'manager'
    ? '/dashboard/manager'
    : '/dashboard/employee';

  return `${basePath}${subpath}`;
}

export default DashboardLayout;
