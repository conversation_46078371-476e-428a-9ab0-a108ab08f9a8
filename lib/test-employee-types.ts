// Test file to verify employee type functionality
// This file can be used to test the employee type integration

import { getEmployeeTypesForCompany, areEmployeeTypesAvailable, getEmployeeTypeById } from './employee';
import { getCompanyCountryCode } from './auth';

/**
 * Test function to verify employee type functionality
 * This function tests the complete employee type workflow
 */
export async function testEmployeeTypeIntegration() {
  console.log('🧪 Testing Employee Type Integration...');

  try {
    // Test 1: Check if country code is available
    console.log('📍 Test 1: Checking company country code...');
    const countryCode = getCompanyCountryCode();
    console.log('Country code:', countryCode);

    if (!countryCode) {
      console.warn('⚠️ No country code found. Make sure user is logged in with company data.');
      return false;
    }

    // Test 2: Check if employee types are available
    console.log('🔍 Test 2: Checking if employee types are available...');
    const available = await areEmployeeTypesAvailable();
    console.log('Employee types available:', available);

    // Test 3: Fetch employee types
    console.log('📋 Test 3: Fetching employee types...');
    const employeeTypes = await getEmployeeTypesForCompany();
    console.log('Employee types:', employeeTypes);
    console.log('Number of employee types:', employeeTypes.length);

    // Test 4: Test getting employee type by ID (if any exist)
    if (employeeTypes.length > 0) {
      console.log('🔎 Test 4: Testing getEmployeeTypeById...');
      const firstType = employeeTypes[0];
      const retrievedType = await getEmployeeTypeById(firstType.employee_type_id);
      console.log('Retrieved employee type:', retrievedType);

      if (retrievedType && retrievedType.employee_type_id === firstType.employee_type_id) {
        console.log('✅ getEmployeeTypeById test passed');
      } else {
        console.log('❌ getEmployeeTypeById test failed');
      }
    }

    // Test 5: Verify API endpoint format
    console.log('🌐 Test 5: Verifying API endpoint format...');
    const expectedEndpoint = `api/countries/${countryCode}/employee-types`;
    console.log('Expected endpoint:', expectedEndpoint);

    // Test 6: Direct API call test
    console.log('🔗 Test 6: Testing direct API call...');
    const { apiGet } = await import('./api');
    const { getAccessToken } = await import('./auth');
    const token = getAccessToken();

    if (token) {
      try {
        const directResponse = await apiGet(`api/countries/${countryCode}/employee-types`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('Direct API response:', directResponse);
      } catch (apiError) {
        console.error('Direct API call failed:', apiError);
      }
    }

    console.log('✅ Employee Type Integration Test Completed');
    return true;

  } catch (error) {
    console.error('❌ Employee Type Integration Test Failed:', error);
    return false;
  }
}

/**
 * Test function to verify employee registration data format
 */
export function testEmployeeRegistrationData() {
  console.log('🧪 Testing Employee Registration Data Format...');
  
  // Sample employee registration data as per requirements
  const sampleEmployeeData = {
    "company_id": "e7d11f4e-3112-4e39-8619-5c7aaeac6554",
    "first_name": "Alex",
    "last_name": "Rugema", 
    "email": "<EMAIL>",
    "id_number": "1238901928383",
    "phone_number": "0781049931",
    "position": "manager",
    "hire_date": "2023-05-15",
    "employee_type_id": "55153851-12a1-42a2-bd9d-998d604ebc07" // Optional field
  };
  
  console.log('📋 Sample employee registration data:');
  console.log(JSON.stringify(sampleEmployeeData, null, 2));
  
  // Verify required fields
  const requiredFields = ['company_id', 'first_name', 'last_name'];
  const optionalFields = ['email', 'id_number', 'phone_number', 'position', 'hire_date', 'employee_type_id'];
  
  console.log('✅ Required fields:', requiredFields);
  console.log('📝 Optional fields:', optionalFields);
  console.log('💡 Note: employee_type_id is optional for attendance-only clients');
  
  return sampleEmployeeData;
}

/**
 * Test function to verify employee update data format
 */
export function testEmployeeUpdateData() {
  console.log('🧪 Testing Employee Update Data Format...');
  
  // Sample employee update data
  const sampleUpdateData = {
    "company_id": "e7d11f4e-3112-4e39-8619-5c7aaeac6554",
    "first_name": "Alex",
    "last_name": "Rugema",
    "email": "<EMAIL>", 
    "id_number": "1238901928383",
    "phone_number": "0781049931",
    "position": "senior manager",
    "hire_date": "2023-05-15",
    "employee_type_id": "55153851-12a1-42a2-bd9d-998d604ebc07"
  };
  
  console.log('📋 Sample employee update data:');
  console.log(JSON.stringify(sampleUpdateData, null, 2));
  console.log('🔄 Update endpoint: api/employees/{employee_id}');
  console.log('📡 HTTP Method: PATCH');
  
  return sampleUpdateData;
}

/**
 * Utility function to log company information from login response
 */
export function logCompanyInformation() {
  console.log('🏢 Checking Company Information...');
  
  // Get auth data from localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) {
    console.log('❌ No auth data found');
    return;
  }
  
  try {
    const parsedData = JSON.parse(authData);
    
    if (parsedData.companies && parsedData.companies.length > 0) {
      const company = parsedData.companies[0];
      console.log('🏢 Company Information:');
      console.log('- Company ID:', company.company_id);
      console.log('- Company Name:', company.company_name);
      
      if (company.company_dict) {
        console.log('- Country Code:', company.company_dict.country_code);
        console.log('- Currency:', company.company_dict.country_currency);
        console.log('- Timezone:', company.company_dict.time_zone);
        console.log('- Date Format:', company.company_dict.date_format);
      }
    }
  } catch (error) {
    console.error('❌ Error parsing auth data:', error);
  }
}
