// types/subscription.ts

export type BillingCycle = 'MONTHLY' | 'YEARLY';

export type SupportLevel = 'email' | 'email_chat' | 'priority';

export interface AIAttendanceAnalytics {
  enabled: boolean;
  monthly_limit: number | null;
}

export interface AnnouncementManagement {
  enabled: boolean;
  monthly_limit: number | null;
}

export interface ShiftManagement {
  enabled: boolean;
  shift_limit: number | null;
}

export interface SubscriptionFeatures {
  employee_self_service: boolean;
  attendance_management: boolean;
  ai_attendance_analytics: AIAttendanceAnalytics;
  leave_management: boolean;
  announcement_management: AnnouncementManagement;
  shift_management: ShiftManagement;
  basic_reports: boolean;
  advanced_reports: boolean;
  support: SupportLevel;
}

export interface SubscriptionPlan {
  plan_id: string;
  name: string;
  description: string;
  flat_price: number;
  price_per_employee: number;
  price: number; // Same as price_per_employee
  billing_cycle: BillingCycle;
  max_employees: number | null;
  sort_order: number;
  is_active: boolean;
  features: SubscriptionFeatures;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPlansResponse {
  count: number;
  message: string;
  plans: SubscriptionPlan[];
}

export interface SubscriptionPlanResponse {
  message: string;
  plan: SubscriptionPlan;
}

export interface CreateSubscriptionPlanRequest {
  name: string;
  description: string;
  flat_price: number;
  price_per_employee: number;
  billing_cycle: BillingCycle;
  max_employees: number | null;
  sort_order: number;
  features: SubscriptionFeatures;
}

export interface UpdateSubscriptionPlanRequest {
  name?: string;
  description?: string;
  flat_price?: number;
  price_per_employee?: number;
  billing_cycle?: BillingCycle;
  max_employees?: number | null;
  sort_order?: number;
  features?: SubscriptionFeatures;
  is_active?: boolean;
}

// For displaying features in a user-friendly way
export interface FeatureDisplay {
  name: string;
  enabled: boolean;
  limit?: number | null;
  description?: string;
}

// AI Analytics Usage Tracking
export interface AIAnalyticsUsage {
  current_month_usage: number;
  monthly_limit: number | null;
  remaining_requests: number | null;
  reset_date: string;
}

// AI Analytics Response Types
export interface AIInsights {
  alerts: string[];
  cached: boolean;
  content: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  recommendations: string[];
  summary: string;
}

export interface AttendanceStatistics {
  absent_count: number;
  attendance_percentage: number;
  days_in_month: number;
  late_count: number;
  month: string;
  on_leave_count: number;
  present_count: number;
}

export interface DepartmentStatistics {
  absent_count: number;
  avg_attendance_percentage: number;
  department_id: string;
  department_name: string;
  employee_count: number;
  late_count: number;
  on_leave_count: number;
  present_count: number;
}

export interface AttendanceMetadata {
  end_date: string;
  period: 'daily' | 'weekly' | 'monthly' | 'annual';
  period_description: string;
  start_date: string;
  total_days: number;
  total_employees: number;
}

export interface AttendanceSummary {
  absent_count: number;
  attendance_percentage: number;
  late_count: number;
  on_leave_count: number;
  present_count: number;
}

export interface AIAnalyticsResponse {
  ai_insights: AIInsights;
  annual_statistics?: AttendanceStatistics[];
  department_statistics: DepartmentStatistics[];
  metadata: AttendanceMetadata;
  status: string;
  summary: AttendanceSummary;
}
