// types/subscription.ts

export type BillingCycle = 'MONTHLY' | 'YEARLY';

export type SupportLevel = 'email' | 'email_chat' | 'priority';

export interface AIAttendanceAnalytics {
  enabled: boolean;
  monthly_limit: number | null;
}

export interface AnnouncementManagement {
  enabled: boolean;
  monthly_limit: number | null;
}

export interface ShiftManagement {
  enabled: boolean;
  shift_limit: number | null;
}

export interface SubscriptionFeatures {
  employee_self_service: boolean;
  attendance_management: boolean;
  ai_attendance_analytics: AIAttendanceAnalytics;
  leave_management: boolean;
  announcement_management: AnnouncementManagement;
  shift_management: ShiftManagement;
  basic_reports: boolean;
  advanced_reports: boolean;
  support: SupportLevel;
}

export interface SubscriptionPlan {
  plan_id: string;
  name: string;
  description: string;
  flat_price: number;
  price_per_employee: number;
  price: number; // Same as price_per_employee
  billing_cycle: BillingCycle;
  max_employees: number | null;
  sort_order: number;
  is_active: boolean;
  features: SubscriptionFeatures;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPlansResponse {
  count: number;
  message: string;
  plans: SubscriptionPlan[];
}

export interface SubscriptionPlanResponse {
  message: string;
  plan: SubscriptionPlan;
}

export interface CreateSubscriptionPlanRequest {
  name: string;
  description: string;
  flat_price: number;
  price_per_employee: number;
  billing_cycle: BillingCycle;
  max_employees: number | null;
  sort_order: number;
  features: SubscriptionFeatures;
}

export interface UpdateSubscriptionPlanRequest {
  name?: string;
  description?: string;
  flat_price?: number;
  price_per_employee?: number;
  billing_cycle?: BillingCycle;
  max_employees?: number | null;
  sort_order?: number;
  features?: SubscriptionFeatures;
  is_active?: boolean;
}

// For displaying features in a user-friendly way
export interface FeatureDisplay {
  name: string;
  enabled: boolean;
  limit?: number | null;
  description?: string;
}
